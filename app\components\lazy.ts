/**
 * 懒加载插件配置
 * 参考官方Demo: https://github.com/dream-num/univer/blob/dev/examples/src/sheets/lazy.ts
 */

import type { Plugin, PluginCtor } from '@univerjs/core';
import { UniverSheetsFormulaPlugin } from '@univerjs/sheets-formula';
import { UniverSheetsFormulaUIPlugin } from '@univerjs/sheets-formula-ui';
import { UniverSheetsNumfmtPlugin } from '@univerjs/sheets-numfmt';
import { UniverSheetsNumfmtUIPlugin } from '@univerjs/sheets-numfmt-ui';
import { UniverDataValidationPlugin } from '@univerjs/data-validation';
import { UniverSheetsDataValidationPlugin } from '@univerjs/sheets-data-validation';
import { UniverSheetsDataValidationUIPlugin } from '@univerjs/sheets-data-validation-ui';
import { UniverSheetsFilterPlugin } from '@univerjs/sheets-filter';
import { UniverSheetsFilterUIPlugin } from '@univerjs/sheets-filter-ui';
import { UniverSheetsSortPlugin } from '@univerjs/sheets-sort';
import { UniverSheetsSortUIPlugin } from '@univerjs/sheets-sort-ui';
import { UniverSheetsConditionalFormattingPlugin } from '@univerjs/sheets-conditional-formatting';
import { UniverSheetsConditionalFormattingUIPlugin } from '@univerjs/sheets-conditional-formatting-ui';
import { UniverSheetsTablePlugin } from '@univerjs/sheets-table';
import { UniverSheetsTableUIPlugin } from '@univerjs/sheets-table-ui';
import { UniverSheetsChartPlugin } from '@univerjs-pro/sheets-chart';
import { UniverSheetsChartUIPlugin } from '@univerjs-pro/sheets-chart-ui';
import { UniverSheetsPivotTablePlugin } from '@univerjs-pro/sheets-pivot';
import { UniverSheetsPivotTableUIPlugin } from '@univerjs-pro/sheets-pivot-ui';

export default function getLazyPlugins(): Array<[PluginCtor<Plugin>] | [PluginCtor<Plugin>, unknown]> {
    return [
        // 基础功能插件
        [UniverSheetsFormulaPlugin],
        [UniverSheetsFormulaUIPlugin],
        [UniverSheetsNumfmtPlugin],
        [UniverSheetsNumfmtUIPlugin],
        [UniverDataValidationPlugin],
        [UniverSheetsDataValidationPlugin],
        [UniverSheetsDataValidationUIPlugin, {
            showEditOnDropdown: false
        }],
        [UniverSheetsFilterPlugin],
        [UniverSheetsFilterUIPlugin],
        [UniverSheetsSortPlugin],
        [UniverSheetsSortUIPlugin],
        [UniverSheetsConditionalFormattingPlugin],
        [UniverSheetsConditionalFormattingUIPlugin],
        [UniverSheetsTablePlugin],
        [UniverSheetsTableUIPlugin],
    ];
}

export function getVeryLazyPlugins(): Array<[PluginCtor<Plugin>] | [PluginCtor<Plugin>, unknown]> {
    return [
        // 高级功能插件
        [UniverSheetsChartPlugin],
        [UniverSheetsChartUIPlugin],
        [UniverSheetsPivotTablePlugin],
        [UniverSheetsPivotTableUIPlugin],
    ];
}
