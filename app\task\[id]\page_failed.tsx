'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { toast } from 'react-hot-toast'
import { logger } from '@/app/lib/logger'
import { performance } from '@/app/lib/performance'
import dynamic from 'next/dynamic'

// 动态导入 Univer 组件
const UniverSheet = dynamic(() => import('@/app/components/UniverSheet'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
        <p className="text-gray-600">表格加载中，马上就好……</p>
      </div>
    </div>
  )
})

interface Task {
  id: string
  name: string
  description: string
  type: string
  difficulty: number
  points: number
  initialData?: Record<string, unknown>
  validationLogic?: string
  operationSteps?: string[]
  completionCriteria?: string
  tips?: string
  level?: {
    id: string
    name: string
    parentId?: string
  }
}

interface TaskPageProps {
  params: {
    id: string
  }
}

export default function TaskPage({ params }: TaskPageProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [task, setTask] = useState<Task | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [taskInitialData, setTaskInitialData] = useState<Record<string, unknown>>({})
  const [isTaskFetched, setIsTaskFetched] = useState(false) // 添加标记防止重复获取任务
  
  // 新增状态管理
  const [taskDescriptionHeight, setTaskDescriptionHeight] = useState<'normal' | 'expanded' | 'full'>('normal')
  const [taskPanelVisible, setTaskPanelVisible] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (status === 'authenticated' && !isTaskFetched) {
      logger.debug('触发任务获取', { session: !!session, taskId: params.id, isTaskFetched })
      fetchTask()
    }
  }, [status, session, params.id, router, isTaskFetched])

  const fetchTask = async () => {
    try {
      setIsTaskFetched(true) // 设置标记防止重复获取
      logger.debug('开始获取任务数据', params.id)
      
      const response = await fetch(`/api/tasks/${params.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch task')
      }
      
      const taskData = await response.json()
      setTask(taskData)
      
      // 设置导航栏数据
      if (taskData.level) {
        const navbarData = {
          showLevelList: true,
          levelListHref: taskData.level.parentId ? `/level/${taskData.level.parentId}` : '/dashboard'
        }
        logger.debug('设置导航栏数据', navbarData)
        
        // 通过自定义事件通知导航栏更新
        window.dispatchEvent(new CustomEvent('updateNavbar', { detail: navbarData }))
      }
      
      // 处理初始数据
      if (taskData.initialData) {
        logger.debug('从API获取到初始数据', taskData.initialData)
        setTaskInitialData(taskData.initialData)
      }
    } catch (error) {
      console.error('Error fetching task:', error)
      toast.error('获取任务失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (workbookData: any) => {
    if (!task) return

    setSubmitting(true)
    const submitTimer = performance.startTimer('task-submit')

    try {
      const response = await fetch(`/api/tasks/${task.id}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ workbookData }),
      })

      const result = await response.json()
      
      if (result.success) {
        toast.success(`任务完成！获得 ${task.points} 经验值`)
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          if (task.level?.parentId) {
            router.push(`/level/${task.level.parentId}`)
          } else {
            router.push('/dashboard')
          }
        }, 2000)
      } else {
        toast.error(result.message || '验证失败，请检查操作步骤')
      }
    } catch (error) {
      console.error('Error submitting task:', error)
      toast.error('提交失败，请重试')
    } finally {
      setSubmitting(false)
      submitTimer.end()
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">任务未找到</h1>
          <p className="text-gray-600 mb-8">请检查任务链接是否正确</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
        <div className="flex gap-4 h-[calc(100vh-12rem)]">
          {/* 任务说明面板 */}
          {taskPanelVisible && (
            <div className="w-96 flex-shrink-0">
              <div className="bg-white shadow-xl rounded-2xl p-6 border border-gray-100 h-full flex flex-col">
                {/* 任务标题 */}
                <div className="mb-6">
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">{task.name}</h1>
                  <span className="inline-block px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                    {task.type}
                  </span>
                </div>

                {/* 任务详情标题 */}
                <div className="flex items-center mb-4">
                  <div className="text-blue-500 mr-2">📋</div>
                  <h2 className="text-lg font-semibold text-gray-800">任务详情</h2>
                </div>

                {/* 任务描述 */}
                <div className={`mb-6 flex-1 overflow-y-auto scrollbar-univer ${
                  taskDescriptionHeight === 'normal'
                    ? 'max-h-[calc(100vh-20rem)]'
                    : taskDescriptionHeight === 'expanded'
                    ? 'max-h-[calc(100vh-12rem)]'
                    : 'max-h-none'
                }`}>
                  <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                    <p className="text-gray-700 mb-4">{task.description}</p>
                    
                    {task.operationSteps && task.operationSteps.length > 0 && (
                      <div className="mb-4">
                        <div className="flex items-center mb-2">
                          <div className="text-green-500 mr-1">📝</div>
                          <span className="font-medium text-gray-800">操作步骤：</span>
                        </div>
                        <ol className="list-none space-y-2">
                          {task.operationSteps.map((step, index) => (
                            <li key={index} className="flex items-start">
                              <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-500 text-white text-sm font-medium rounded-full mr-3 mt-0.5 flex-shrink-0">
                                {index + 1}
                              </span>
                              <span className="text-gray-700">{step}</span>
                            </li>
                          ))}
                        </ol>
                      </div>
                    )}

                    {task.completionCriteria && (
                      <div className="mb-4">
                        <div className="flex items-center mb-2">
                          <div className="text-green-500 mr-1">📝</div>
                          <span className="font-medium text-gray-800">{task.completionCriteria}</span>
                        </div>
                      </div>
                    )}

                    {task.tips && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex items-start">
                          <div className="text-yellow-500 mr-2">💡</div>
                          <div>
                            <span className="font-medium text-yellow-800">提示：</span>
                            <span className="text-yellow-700">{task.tips}</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 任务信息 */}
                <div className="mb-6 grid grid-cols-2 gap-4">
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100">
                    <div className="flex items-center justify-center mb-2">
                      <span className="text-2xl font-bold text-purple-600">{task.points}</span>
                      <div className="ml-2">
                        <div className="text-purple-500">💎</div>
                        <span className="text-xs text-purple-600">经验值</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-4 border border-yellow-100">
                    <div className="flex items-center justify-center mb-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className={`text-lg ${i < task.difficulty ? 'text-yellow-400' : 'text-gray-300'}`}>
                            ★
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-yellow-500">⚡</div>
                      <span className="text-xs text-yellow-600">难度等级</span>
                    </div>
                  </div>
                </div>

                {/* 控制按钮区域 */}
                <div className="flex items-center justify-between mb-4">
                  {/* 扩展按钮 */}
                  <button
                    onClick={() => {
                      if (taskDescriptionHeight === 'normal') {
                        setTaskDescriptionHeight('expanded')
                      } else if (taskDescriptionHeight === 'expanded') {
                        setTaskDescriptionHeight('full')
                      } else {
                        setTaskDescriptionHeight('normal')
                      }
                    }}
                    className="flex items-center justify-center w-6 h-6 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors duration-200 shadow-sm"
                    title={
                      taskDescriptionHeight === 'normal' 
                        ? '展开任务说明' 
                        : taskDescriptionHeight === 'expanded'
                        ? '完全展开'
                        : '收起任务说明'
                    }
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d={taskDescriptionHeight === 'full' ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} 
                      />
                    </svg>
                  </button>

                  {/* 面板切换按钮 */}
                  <button
                    onClick={() => setTaskPanelVisible(false)}
                    className="flex items-center justify-center w-6 h-6 bg-gray-500 hover:bg-gray-600 text-white rounded-full transition-colors duration-200 shadow-sm"
                    title="隐藏任务说明面板"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                </div>

                {/* 提交按钮 */}
                <button
                  onClick={() => {
                    const univerSheet = document.querySelector('[data-univer-sheet]') as any
                    if (univerSheet && univerSheet.getWorkbookData) {
                      handleSubmit(univerSheet.getWorkbookData())
                    } else {
                      toast.error('无法获取工作表数据，请刷新页面重试')
                    }
                  }}
                  disabled={submitting}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 shadow-lg disabled:shadow-none flex items-center justify-center"
                >
                  {submitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      提交中...
                    </>
                  ) : (
                    <>
                      🚀 提交任务
                      <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* 面板显示按钮 - 当面板隐藏时显示 */}
          {!taskPanelVisible && (
            <button
              onClick={() => setTaskPanelVisible(true)}
              className="fixed left-4 top-1/2 transform -translate-y-1/2 flex items-center justify-center w-6 h-6 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors duration-200 shadow-lg z-10"
              title="显示任务说明面板"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}

          {/* Excel练习区域 */}
          <div className="flex-1 h-full">
            <div className="bg-white shadow-xl rounded-2xl p-6 border border-gray-100 h-full flex flex-col">
              {/* Excel练习区头部 */}
              <div className="flex items-center mb-6">
                <div className="text-green-500 mr-2 text-xl">📊</div>
                <h2 className="text-xl font-semibold text-gray-800">Excel练习区</h2>
              </div>

              {/* Univer 组件容器 */}
              <div className="flex-1 min-h-0">
                <UniverSheet 
                  initialData={taskInitialData}
                  onDataChange={(data) => {
                    // 可以在这里处理数据变化
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
