'use client'

import { useSession } from 'next-auth/react'
import { log } from '@/app/lib/logger';
import { useEffect, useState, useCallback, useRef } from 'react'
import { useRouter, useParams } from 'next/navigation'
import dynamic from 'next/dynamic'
import { validateTask } from '@/app/lib/validation'
import { UniverInstance, UniverAPI } from '@/types/univer'
import { useNavbar } from '../../components/NavbarContext'

// 动态导入UniverWrapper组件
const UniverWrapper = dynamic(() => import('@/app/components/UniverWrapper'), {
  ssr: false,
  loading: () => (
    <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
      {/* <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div> */}
      {/* <div className="text-lg font-medium text-gray-700 mb-2">表格加载中，马上就好……</div> */}
      {/* <div className="text-sm text-gray-500">正在初始化Excel组件</div> */}
    </div>
  )
})

interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface Level {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  parentId?: string
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
    attempts: number
  }>
}

export default function TaskPage() {
  const { data: session, status, update } = useSession()
  const router = useRouter()
  const params = useParams()
  const { setNavbarData } = useNavbar()
  const setNavbarDataRef = useRef(setNavbarData)
  const taskId = params.id as string

  // 更新ref以保持最新的setNavbarData引用
  useEffect(() => {
    setNavbarDataRef.current = setNavbarData
  }, [setNavbarData])
  
  const [level, setLevel] = useState<Level | null>(null)
  const [currentTask, setCurrentTask] = useState<Task | null>(null)
  const [, setUniverInstance] = useState<UniverInstance | null>(null)

  const [univerAPI, setUniverAPI] = useState<UniverAPI | null>(null)
  const [loading, setLoading] = useState(true)
  const [message, setMessage] = useState('')
  const [submitting, setSubmitting] = useState(false)
  const [taskInitialData, setTaskInitialData] = useState<Record<string, unknown>>({})
  const [isTaskFetched, setIsTaskFetched] = useState(false) // 添加标记防止重复获取任务
  const [taskDescriptionHeight, setTaskDescriptionHeight] = useState<'normal' | 'expanded' | 'full'>('normal') // 任务描述区域高度状态

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  const fetchTask = useCallback(async () => {
    // 防止重复获取相同任务
    if (isTaskFetched) {
      log.debug('任务已获取，跳过重复请求')
      return
    }

    try {
      log.debug('开始获取任务数据:', taskId)
      const response = await fetch(`/api/tasks/${taskId}`)
      if (response.ok) {
        const taskData = await response.json()
        setCurrentTask(taskData.task)
        setLevel(taskData.level)
        setIsTaskFetched(true) // 标记任务已获取

        // 设置导航栏数据，显示级别列表链接
        const levelListHref = taskData.level.parentId ? `/level/${taskData.level.parentId}` : '/dashboard'
        log.debug('设置导航栏数据:', { showLevelList: true, levelListHref })
        setNavbarDataRef.current({
          showLevelList: true,
          levelListHref: levelListHref
        })

        // 设置初始数据 - 只在数据真正存在时设置
        if (taskData.task.initialData) {
          try {
            const initialData = JSON.parse(taskData.task.initialData)
            log.debug('从API获取到初始数据:', initialData)
            setTaskInitialData(initialData)
          } catch (error) {
            log.error('解析初始数据失败:', error)
            setTaskInitialData({})
          }
        } else {
          // 如果没有初始数据，确保清空状态
          setTaskInitialData({})
        }
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      log.error('获取任务失败:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [taskId, isTaskFetched, router]) // 移除setNavbarData依赖，避免循环

  useEffect(() => {
    if (session && taskId && !isTaskFetched) {
      log.debug('触发任务获取:', { session: !!session, taskId, isTaskFetched })
      fetchTask()
    }
  }, [session, taskId, fetchTask, isTaskFetched])

  // 组件卸载时清理导航栏状态
  useEffect(() => {
    return () => {
      // 清理导航栏状态，避免状态残留
      setNavbarDataRef.current({
        showLevelList: false,
        levelListHref: undefined
      })
    }
  }, []) // 使用ref避免依赖问题

  const validateCurrentTask = async (task: Task) => {
    if (!univerAPI) {
      setMessage('Excel组件未加载完成，请稍后重试')
      return false
    }

    try {
      const validationRule = JSON.parse(task.validation)
      const result = await validateTask(univerAPI, validationRule)
      
      if (!result.success) {
        setMessage(result.message)
      }
      
      return result.success
    } catch (error) {
      log.error('验证任务失败:', error)
      setMessage('验证过程中发生错误，请检查任务配置')
      return false
    }
  }

  // 处理Univer实例变化 - 使用useCallback稳定引用
  const handleUniverReady = useCallback((instance: UniverInstance, api: UniverAPI) => {
    setUniverInstance(instance)
    setUniverAPI(api)
    log.univer('Univer实例已准备就绪')
  }, [])

  const handleTaskSubmit = async () => {
    if (!currentTask) return
    
    setSubmitting(true)
    setMessage('')
    
    const isValid = await validateCurrentTask(currentTask)
    
    if (isValid) {
      try {
        // 提交任务完成状态
        const response = await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            taskId: currentTask.id,
            levelId: level?.id,
            completed: true,
            score: level?.points || 0
          })
        })
        
        if (response.ok) {
          setMessage('闯关完成！3秒后返回关卡列表...')

          // 延迟更新session，避免立即触发Excel工作区重新加载
          // 在显示成功消息3秒后更新积分，这样用户能看到积分变化但不影响Excel工作区
          setTimeout(async () => {
            try {
              await update()
              log.debug('Session积分已更新')
            } catch (error) {
              log.error('更新session失败:', error)
            }
          }, 3000)

          setTimeout(() => {
            // 跳转到父级关卡（主任务）页面
            // 如果当前关卡有parentId，说明它是子关卡，应该跳转到父级主任务
            // 如果没有parentId，说明它本身就是主任务
            // const targetLevelId = level?.parentId || level?.id
            if (level?.parentId) {
              // 子关卡完成后跳转到父级主任务页面
              router.push(`/level/${level.parentId}`)
            } else {
              // 主任务完成后跳转到dashboard
              router.push('/dashboard')
            }
          }, 3000)
        } else {
          setMessage('提交失败，请重试')
        }
      } catch (error) {
        log.error('提交任务失败:', error)
        setMessage('提交失败，可能是网络原因，请重试')
      }
    } else {
      setMessage('闯关失败，\n请检查你的操作是否正确')
    }
    
    setSubmitting(false)
  }



  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session || !currentTask || !level) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 h-[calc(100vh-12rem)]">
          {/* 任务说明面板 */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow-xl rounded-2xl p-6 border border-gray-100 h-full flex flex-col">
              {/* 任务头部 - 更紧凑 */}
              <div className="text-center mb-6">
                <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                  {currentTask.name}
                </h1>
                <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  {currentTask.type}
                </div>
              </div>

              {/* 任务详情标题 */}
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-blue-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs">📋</span>
                </div>
                <h2 className="text-base font-bold text-gray-900">
                  任务详情
                </h2>
              </div>

              {/* 任务描述 */}
              <div className={`mb-4 flex-1 overflow-y-auto scrollbar-univer ${
                taskDescriptionHeight === 'normal'
                  ? 'max-h-[calc(100vh-18rem)]'  // 进一步增加高度，利用按钮区域节省的空间
                  : taskDescriptionHeight === 'expanded'
                  ? 'max-h-[calc(100vh-10rem)]'  // 进一步扩大
                  : 'max-h-none'  // 显示全部内容
              }`}>
                <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                  {currentTask.description.split('\n').map((line, index) => {
                    // 处理操作步骤
                    if (line.trim().match(/^\d+\./)) {
                      return (
                        <div key={index} className="flex items-start mb-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                          <span className="flex w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs rounded-full items-center justify-center mr-2 mt-0.5 flex-shrink-0 font-bold shadow-sm">
                            {line.trim().match(/^(\d+)\./)?.[1]}
                          </span>
                          <span className="text-gray-800 text-sm leading-relaxed">{line.replace(/^\d+\.\s*/, '')}</span>
                        </div>
                      )
                    }
                    // 处理标题行
                    else if (line.includes('操作步骤：') || line.includes('任务说明：') || line.includes('完成后')) {
                      return (
                        <div key={index} className="font-bold text-gray-900 mt-4 mb-3 text-base flex items-center">
                          <span className="mr-2 text-sm">📝</span>
                          {line}
                        </div>
                      )
                    }
                    // 处理提示信息
                    else if (line.includes('提示：')) {
                      return (
                        <div key={index} className="mt-3 p-3 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                          <div className="flex items-start">
                            <span className="text-yellow-600 text-sm mr-2">💡</span>
                            <span className="text-gray-800 text-sm leading-relaxed">
                              <span className="font-semibold text-yellow-800">提示：</span>
                              {line.replace('提示：', '')}
                            </span>
                          </div>
                        </div>
                      )
                    }
                    // 处理普通文本行
                    else if (line.trim()) {
                      return (
                        <div key={index} className="mb-1 text-gray-700 text-sm leading-relaxed">
                          {line}
                        </div>
                      )
                    }
                    return null
                  })}
                </div>
              </div>

              {/* 展开按钮 - 缩小尺寸和间距 */}
              {taskDescriptionHeight !== 'full' && (
                <div className="flex justify-center mb-2">
                  <button
                    onClick={() => {
                      if (taskDescriptionHeight === 'normal') {
                        setTaskDescriptionHeight('expanded')
                      } else {
                        setTaskDescriptionHeight('full')
                      }
                    }}
                    className="flex items-center justify-center w-6 h-6 bg-gray-100 hover:bg-gray-200 rounded-full border border-gray-300 transition-colors duration-200 group"
                    title={taskDescriptionHeight === 'normal' ? '展开任务详情' : '显示全部内容'}
                  >
                    <svg
                      className="w-3 h-3 text-gray-600 group-hover:text-gray-800 transition-colors duration-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              )}

              {/* 收起按钮 - 缩小尺寸和间距 */}
              {taskDescriptionHeight === 'full' && (
                <div className="flex justify-center mb-2">
                  <button
                    onClick={() => setTaskDescriptionHeight('normal')}
                    className="flex items-center justify-center w-6 h-6 bg-gray-100 hover:bg-gray-200 rounded-full border border-gray-300 transition-colors duration-200 group"
                    title="收起任务详情"
                  >
                    <svg
                      className="w-3 h-3 text-gray-600 group-hover:text-gray-800 transition-colors duration-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </button>
                </div>
              )}

              {/* 统计信息 */}
              <div className="grid grid-cols-2 gap-3 mb-6">
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-200 text-center">
                  <div className="text-xl font-bold text-green-700 mb-1">{level.points}</div>
                  <div className="text-xs text-green-600 flex items-center justify-center">
                    <span className="mr-1 text-xs">💎</span>
                    经验值
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-3 border border-yellow-200 text-center">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    {[...Array(5)].map((_, i) => (
                      <span
                        key={i}
                        className={`text-sm ${
                          i < level.difficulty ? 'text-yellow-500' : 'text-gray-300'
                        }`}
                      >
                        ★
                      </span>
                    ))}
                  </div>
                  <div className="text-xs text-yellow-600 flex items-center justify-center">
                    <span className="mr-1 text-xs">⚡</span>
                    难度等级
                  </div>
                </div>
              </div>

              {/* 任务完成/未完成提示 */}
              {message && (
                <div className={`rounded-xl p-4 mb-6 border ${
                  message.includes('完成') || message.includes('恭喜') || message.includes('通过') || message.includes('成功')
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-800 border-green-200'
                    : 'bg-gradient-to-r from-red-50 to-pink-50 text-red-800 border-red-200'
                }`}>
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      message.includes('完成') || message.includes('恭喜') || message.includes('通过') || message.includes('成功')
                        ? 'bg-green-500'
                        : 'bg-red-500'
                    }`}>
                      <span className="text-white text-sm">
                        {message.includes('完成') || message.includes('恭喜') || message.includes('通过') || message.includes('成功') ? '✅' : '❌'}
                      </span>
                    </div>
                    <div className="font-medium whitespace-pre-line">{message}</div>
                  </div>
                </div>
              )}

              {/* 提交按钮 */}
              <button
                onClick={handleTaskSubmit}
                disabled={submitting}
                className="w-full flex justify-center items-center py-4 px-6 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                {submitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    验证中...
                  </>
                ) : (
                  <>
                    🚀 提交任务
                    <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Excel练习区域 */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow-xl rounded-2xl p-6 border border-gray-100 h-full flex flex-col">
              {/* Excel练习区头部 */}
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center shadow-md">
                  <span className="text-white text-sm">📊</span>
                </div>
                <h2 className="text-lg font-bold text-gray-900">
                  练习区
                </h2>
              </div>

              {/* Excel工作区 */}
              <div className="border-2 border-gray-200 rounded-xl overflow-hidden shadow-inner bg-gray-50 flex-1">
                <UniverWrapper
                  onReady={handleUniverReady}
                  initialData={taskInitialData}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}